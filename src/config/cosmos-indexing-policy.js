/**
 * Optimized Cosmos DB Indexing Policies for Lab Tests Performance
 * This configuration provides optimized indexing for search operations
 */

const labTestsIndexingPolicy = {
  indexingMode: 'consistent',
  automatic: true,
  includedPaths: [
    {
      path: '/DisplayName/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/SHORTNAME/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/LONG_COMMON_NAME/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/CLASS/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/id/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
  ],
  excludedPaths: [
    {
      path: '/COMPONENT/*',
    },
    {
      path: '/PROPERTY/*',
    },
    {
      path: '/TIME_ASPCT/*',
    },
    {
      path: '/SYSTEM/*',
    },
    {
      path: '/SCALE_TYP/*',
    },
    {
      path: '/METHOD_TYP/*',
    },
    {
      path: '/VersionLastChanged/*',
    },
    {
      path: '/CHNG_TYPE/*',
    },
    {
      path: '/DefinitionDescription/*',
    },
    {
      path: '/STATUS/*',
    },
    {
      path: '/CONSUMER_NAME/*',
    },
    {
      path: '/CLASSTYPE/*',
    },
    {
      path: '/FORMULA/*',
    },
    {
      path: '/EXMPL_ANSWERS/*',
    },
    {
      path: '/SURVEY_QUEST_TEXT/*',
    },
    {
      path: '/SURVEY_QUEST_SRC/*',
    },
    {
      path: '/UNITSREQUIRED/*',
    },
    {
      path: '/RELATEDNAMES2/*',
    },
    {
      path: '/ORDER_OBS/*',
    },
    {
      path: '/HL7_FIELD_SUBFIELD_ID/*',
    },
    {
      path: '/EXTERNAL_COPYRIGHT_NOTICE/*',
    },
    {
      path: '/EXAMPLE_UNITS/*',
    },
    {
      path: '/EXAMPLE_UCUM_UNITS/*',
    },
    {
      path: '/STATUS_REASON/*',
    },
    {
      path: '/STATUS_TEXT/*',
    },
    {
      path: '/CHANGE_REASON_PUBLIC/*',
    },
    {
      path: '/COMMON_TEST_RANK/*',
    },
    {
      path: '/COMMON_ORDER_RANK/*',
    },
    {
      path: '/HL7_ATTACHMENT_STRUCTURE/*',
    },
    {
      path: '/EXTERNAL_COPYRIGHT_LINK/*',
    },
    {
      path: '/PanelType/*',
    },
    {
      path: '/AskAtOrderEntry/*',
    },
    {
      path: '/AssociatedObservations/*',
    },
    {
      path: '/VersionFirstReleased/*',
    },
    {
      path: '/ValidHL7AttachmentRequest/*',
    },
  ],
  compositeIndexes: [
    [
      {
        path: '/DisplayName',
        order: 'ascending',
      },
      {
        path: '/CLASS',
        order: 'ascending',
      },
    ],
    [
      {
        path: '/SHORTNAME',
        order: 'ascending',
      },
      {
        path: '/CLASS',
        order: 'ascending',
      },
    ],
    [
      {
        path: '/CLASS',
        order: 'ascending',
      },
      {
        path: '/id',
        order: 'ascending',
      },
    ],
  ],
}

const organizationTestsIndexingPolicy = {
  indexingMode: 'consistent',
  automatic: true,
  includedPaths: [
    {
      path: '/organizationId/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/testId/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'String',
          precision: -1,
        },
      ],
    },
    {
      path: '/isActive/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'Boolean',
          precision: -1,
        },
      ],
    },
    {
      path: '/price/?',
      indexes: [
        {
          kind: 'Range',
          dataType: 'Number',
          precision: -1,
        },
      ],
    },
  ],
  excludedPaths: [
    {
      path: '/departments/*',
    },
    {
      path: '/created_on/*',
    },
    {
      path: '/updated_on/*',
    },
  ],
  compositeIndexes: [
    [
      {
        path: '/organizationId',
        order: 'ascending',
      },
      {
        path: '/isActive',
        order: 'ascending',
      },
    ],
    [
      {
        path: '/organizationId',
        order: 'ascending',
      },
      {
        path: '/testId',
        order: 'ascending',
      },
    ],
  ],
}

module.exports = {
  labTestsIndexingPolicy,
  organizationTestsIndexingPolicy,
}
