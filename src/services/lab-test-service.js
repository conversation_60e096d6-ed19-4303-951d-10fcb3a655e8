const testRepository = require('../repositories/lab-test-repository')
const asyncJobService = require('./async-job-service')
const {
  classToDepartmentMapping,
} = require('../common/class-department-mapping')

class TestService {
  async getTestsByLoincNums(loincNums) {
    try {
      return await testRepository.findByLoincNums(loincNums)
    } catch (error) {
      console.error('Error in getTestsByLoincNums:', error)
      throw new Error('Failed to fetch tests by LOINC numbers')
    }
  }

  async bulkInsertTests(tests) {
    try {
      return await testRepository.bulkInsertTest(tests)
    } catch (error) {
      console.error('Error in bulkInsertTests:', error)
      throw new Error('Failed to bulk insert tests')
    }
  }

  async getAllLabTests() {
    try {
      const labTests = await testRepository.getAllLabTests()
      return labTests.map((test) => ({
        id: test.id,
        name: test.DisplayName || test.SHORTNAME || test.LONG_COMMON_NAME || '',
      }))
    } catch (error) {
      console.error('Error in service while fetching lab tests:', error)
      throw new Error('Failed to fetch lab tests from service')
    }
  }

  async searchLabTests(searchText, pageSize, continuationToken, department) {
    try {
      const result = await testRepository.searchLabTests(
        searchText,
        pageSize,
        continuationToken,
        department,
      )
      return {
        items: result.items.map((test) => ({
          id: test.id,
          name:
            test.DisplayName || test.SHORTNAME || test.LONG_COMMON_NAME || '',
        })),
        continuationToken: result.nextToken,
      }
    } catch (error) {
      console.error('Error in service while searching lab tests:', error)
      throw new Error('Failed to search lab tests from service')
    }
  }

  async searchOrganizationLabTests(
    organizationId,
    searchText,
    pageSize = 1000,
    continuationToken = null,
    department = null,
  ) {
    try {
      // Validate inputs
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }

      if (!searchText || !searchText.trim()) {
        throw new Error('Search text is required')
      }

      console.log(
        `🔍 Service: Searching for "${searchText}" in org ${organizationId}, pageSize=${pageSize}, dept=${department}`,
      )

      // Use optimized search method for better performance
      const result = await testRepository.searchOrganizationLabTestsOptimized(
        organizationId,
        searchText,
        pageSize,
        continuationToken,
        department,
      )

      console.log(
        `📊 Service: Repository returned ${result.items?.length || 0} items`,
      )

      // Transform results to match expected format
      return {
        items: result.items.map((test) => ({
          id: test.loincNum,
          name: test.displayName || test.shortName || test.longCommonName || '',
          class: test.class,
          shortName: test.shortName,
          longCommonName: test.longCommonName,
          displayName: test.displayName,
          organizationPrice: test.organizationCost,
          organizationDepartments: [], // Will be populated if needed
        })),
        continuationToken: result.continuationToken,
        hasMoreResults: result.hasMoreResults,
        totalFetched: result.totalFetched,
      }
    } catch (error) {
      console.error(
        `Error searching organization lab tests for org ${organizationId}:`,
        error,
      )
      throw new Error('Failed to search organization lab tests')
    }
  }

  async getDepartments() {
    return Object.values(classToDepartmentMapping)
  }

  async getLoincList(
    searchText,
    department,
    organizationId,
    pageSize,
    continuationToken,
    page,
    status = null,
  ) {
    try {
      return await testRepository.fetchLoincList(
        searchText,
        department,
        organizationId,
        pageSize,
        continuationToken,
        page,
        status,
      )
    } catch (error) {
      console.error('Error fetching LOINC list:', error)
      throw new Error('Failed to fetch LOINC list')
    }
  }

  async importLoincData(file) {
    try {
      const parsedData = await parseExcelFile(file) // Assume utility for parsing Excel
      return await testRepository.bulkInsertLoincData(parsedData)
    } catch (error) {
      console.error('Error importing LOINC data:', error)
      throw new Error('Failed to import LOINC data')
    }
  }

  async updateOrganizationTests(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      return await testRepository.updateOrganizationTests(
        organizationId,
        tests,
        department,
        selectAll,
      )
    } catch (error) {
      console.error('Error updating organization test details:', error)
      throw new Error('Failed to update organization test details')
    }
  }

  // Start async organization tests update and return job ID
  async startAsyncOrganizationTestsUpdate(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      const jobData = {
        organizationId,
        tests,
        department,
        selectAll,
      }

      // Start async job
      const jobId = asyncJobService.startJob(
        'LOINC_UPDATE',
        jobData,
        async (data, progressCallback) => {
          return await testRepository.updateOrganizationTestsAsync(
            data.organizationId,
            data.tests,
            data.department,
            data.selectAll,
            progressCallback,
          )
        },
      )

      return { jobId }
    } catch (error) {
      console.error('Error starting async organization tests update:', error)
      throw new Error('Failed to start async organization tests update')
    }
  }

  async removeOrganizationTests(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      return await testRepository.removeOrganizationTests(
        organizationId,
        tests,
        department,
        selectAll,
      )
    } catch (error) {
      console.error('Error removing organization test details:', error)
      throw new Error('Failed to remove organization test details')
    }
  }

  // Start async organization tests removal and return job ID
  async startAsyncOrganizationTestsRemoval(
    organizationId,
    tests,
    department = null,
    selectAll = false,
  ) {
    try {
      const jobData = {
        organizationId,
        tests,
        department,
        selectAll,
      }

      // Start async job
      const jobId = asyncJobService.startJob(
        'LOINC_REMOVE',
        jobData,
        async (data, progressCallback) => {
          return await testRepository.removeOrganizationTestsAsync(
            data.organizationId,
            data.tests,
            data.department,
            data.selectAll,
            progressCallback,
          )
        },
      )

      return { jobId }
    } catch (error) {
      console.error('Error starting async organization tests removal:', error)
      throw new Error('Failed to start async organization tests removal')
    }
  }

  // Get job status
  getJobStatus(jobId) {
    return asyncJobService.getJobStatus(jobId)
  }

  async fetchLoincTestsForOrganization(organizationId) {
    try {
      return await testRepository.fetchLoincTestsForOrganization(organizationId)
    } catch (error) {
      console.error('Error fetching LOINC tests for organization:', error)
      throw new Error('Failed to fetch LOINC tests for organization')
    }
  }
}

module.exports = new TestService()
